# Houzez Ads Extension - Shortcodes & Implementation Guide

## Overview
This guide covers all available shortcodes and implementation methods for displaying ads in the Houzez theme.

## User Types & Permissions

The system automatically detects user types and shows appropriate options:

### 🏢 **Agency Users**
- **Ad Types**: Profile Promotion, Property Promotion
- **Zones**: Homepage & Sidebar (Profile) / All zones (Property)
- **Duration**: 7, 14, 30 days (selectable)
- **Features**: Full campaign creation access with profile and property promotion options

### 👤 **Agent Users**
- **Ad Types**: Property Promotion only (automatic)
- **Zones**: Search Results only (fixed)
7, 14, 30 days (selectable)
- **Features**: Simplified form focused on property promotion in search results

### 🏗️ **Business/Contractor Users**
- **Ad Types**: Business Partnership only (automatic)
- **Zones**: All zones available
- **Duration**: 7, 14, 30 days (selectable)
- **Features**: Banner image/link upload with preview and guidelines

> **Note**: Business users must be manually set by admin in **Ad Campaign Settings > User Type Management**

## Available Shortcodes

### 1. Homepage Agency Profiles
**Shortcode**: `[houzez_ads_homepage_agencies]`

**Purpose**: Displays agency profile cards on the homepage featuring agencies with active campaigns.

**Parameters**:
- `limit` - Number of agencies to display (default: 1)
- `class` - Additional CSS class (default: 'houzez-ads-homepage-agencies')

**Examples**:
```php
// Display single agency profile
[houzez_ads_homepage_agencies]

// Display up to 3 agencies with custom class
[houzez_ads_homepage_agencies limit="3" class="my-custom-agencies"]
```

**Usage**:
- Add to homepage content areas
- Use in widgets or page builders
- Perfect for hero sections or featured content areas

---

### 2. Sidebar Agency Profiles
**Shortcode**: `[houzez_ads_sidebar_agencies]`

**Purpose**: Shows multiple agency profiles in a compact sidebar format.

**Parameters**:
- `limit` - Number of agencies to display (default: 3)
- `class` - Additional CSS class (default: 'houzez-ads-sidebar-agencies')

**Examples**:
```php
// Display 3 agencies in sidebar
[houzez_ads_sidebar_agencies]

// Display 5 agencies with custom styling
[houzez_ads_sidebar_agencies limit="5" class="featured-agencies"]
```

**Usage**:
- Add to sidebar widgets
- Use in property detail page sidebars
- Perfect for "Featured Agencies" sections

---

### 3. Business Partnership Ads
**Shortcode**: `[houzez_ads_business_partnership]`

**Purpose**: Displays business partnership banner ads (images/HTML content).

**Parameters**:
- `zone` - Ad zone location (default: 'property_detail')
- `class` - Additional CSS class (default: 'houzez-ads-business-partnership')

**Available Zones**:
- `homepage` - Homepage banners
- `sidebar` - Sidebar ads
- `search` - Search results
- `property_detail` - Property detail pages

**Examples**:
```php
// Display business partnership ad on property detail page
[houzez_ads_business_partnership]

// Display homepage business partnership banner
[houzez_ads_business_partnership zone="homepage"]

// Custom styled sidebar business ad
[houzez_ads_business_partnership zone="sidebar" class="my-business-ads"]
```

**Usage**:
- Add to property detail pages
- Use in content areas where banner ads are needed
- Perfect for sponsored content sections

---

### 4. General Ad Zone Display
**Shortcode**: `[houzez_ads_zone]`

**Purpose**: Displays any type of ad from a specific zone.

**Parameters**:
- `zone` - Ad zone (required)
- `class` - Additional CSS class

**Examples**:
```php
// Display homepage ads
[houzez_ads_zone zone="homepage"]

// Display sidebar ads with custom class
[houzez_ads_zone zone="sidebar" class="custom-sidebar-ads"]
```

---

## Automatic Ad Display Features

### Promoted Properties in Search Results
**Feature**: Automatically displays promoted properties after every 5 properties in search results.

**How it works**:
- No shortcode needed - works automatically
- Shows actual promoted properties from campaigns
- Displays with special "Promoted" styling
- Tracks impressions automatically

**Styling**: Properties appear with:
- Orange border and glow effect
- "Promoted" badge in top-right corner
- Subtle animation effects
- Enhanced hover effects

---

## Implementation Locations

### Homepage Implementation
```php
// In homepage template or content
[houzez_ads_homepage_agencies limit="1"]

// For business partnership banners
[houzez_ads_business_partnership zone="homepage"]
```

### Sidebar Implementation
```php
// In sidebar.php or widget areas
[houzez_ads_sidebar_agencies limit="3"]

// For business partnership sidebar ads
[houzez_ads_business_partnership zone="sidebar"]
```

### Property Detail Pages
```php
// In single-property.php or content areas
[houzez_ads_business_partnership zone="property_detail"]
```

### Search Results
- Promoted properties appear automatically
- No manual implementation needed
- Works on all property listing pages

---

## Widget Implementation

### Text Widget
1. Go to **Appearance > Widgets**
2. Add a **Text** or **Custom HTML** widget
3. Insert the shortcode:
```php
[houzez_ads_homepage_agencies limit="2"]
```

### Page Builder Integration
Most page builders support shortcodes:

**Elementor**:
1. Add **Shortcode** widget
2. Insert shortcode in the content field

**WPBakery**:
1. Add **Raw HTML** element
2. Insert shortcode

**Gutenberg**:
1. Add **Shortcode** block
2. Enter shortcode

---

## Theme File Integration

### Direct PHP Implementation
```php
// In theme files (header.php, footer.php, etc.)
<?php echo do_shortcode('[houzez_ads_homepage_agencies limit="1"]'); ?>

// With conditional logic
<?php if (is_front_page()) : ?>
    <?php echo do_shortcode('[houzez_ads_homepage_agencies]'); ?>
<?php endif; ?>
```

### Hook Integration
```php
// Add to functions.php
function add_homepage_agencies() {
    if (is_front_page()) {
        echo do_shortcode('[houzez_ads_homepage_agencies limit="2"]');
    }
}
add_action('houzez_after_header', 'add_homepage_agencies');
```

---

## Styling Customization

### CSS Classes
Each shortcode generates specific CSS classes for customization:

```css
/* Homepage agencies */
.houzez-ads-homepage-agencies .houzez-agency-card {
    /* Custom styles */
}

/* Sidebar agencies */
.houzez-ads-sidebar-agencies .houzez-agency-card-sidebar {
    /* Custom styles */
}

/* Business partnership ads */
.houzez-ads-business-partnership .houzez-ad-campaign {
    /* Custom styles */
}

/* Promoted properties */
.houzez-promoted-property {
    /* Custom styles */
}
```

### Responsive Customization
```css
@media (max-width: 768px) {
    .houzez-agency-card-homepage .agency-card-content {
        flex-direction: column;
    }
}
```

---

## Campaign Requirements

### For Agency Profile Display
- Campaign must have `ad_type` = 'profile'
- Campaign status must be 'approved'
- Campaign must be within active date range
- User must have agency role and profile information filled

### For Business Partnership Display
- Campaign must have `ad_type` = 'partner'
- Campaign status must be 'approved'
- Campaign must have banner image and link
- Campaign must be within active date range
- User must be manually set as business type by admin

### For Promoted Properties
- Campaign must have `ad_type` = 'property'
- Campaign status must be 'approved'
- Campaign must have selected properties
- Properties must be published and active
- Available to both agency and agent users

---

## Troubleshooting

### Shortcode Not Displaying
1. Check if campaigns exist and are approved
2. Verify campaign dates are current
3. Ensure proper ad type is selected
4. Check if user has required profile data

### Styling Issues
1. Clear any caching plugins
2. Check for CSS conflicts
3. Verify theme compatibility
4. Use browser developer tools to inspect

### Performance Optimization
1. Limit the number of displayed items
2. Use caching plugins
3. Optimize images in campaigns
4. Monitor database queries

---

## Quick Reference

| Shortcode | Purpose | Best Location |
|-----------|---------|---------------|
| `[houzez_ads_homepage_agencies]` | Agency profiles | Homepage, hero sections |
| `[houzez_ads_sidebar_agencies]` | Compact agency list | Sidebars, widgets |
| `[houzez_ads_business_partnership]` | Banner ads | Content areas, detail pages |
| `[houzez_ads_zone zone="X"]` | General ads | Any location |

**Auto Features**:
- Promoted properties: Automatic in search results
- Impression tracking: Automatic for all displays
- Responsive design: Built-in for all components
